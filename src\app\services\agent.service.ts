import { Injectable, inject } from '@angular/core';
import { Auth } from '@angular/fire/auth';
import { Functions, httpsCallable } from '@angular/fire/functions';
import { Observable, from, BehaviorSubject, interval } from 'rxjs';
import { switchMap, takeWhile, map, catchError } from 'rxjs/operators';
import { AgentStatus, ProcessingSession, ChatMessage } from '../models/podcast-description.model';

export interface ProcessingRequest {
  sessionId: string;
  audioFileUrl: string;
  userId: string;
  autoMode?: boolean;
}

export interface ChatRequest {
  sessionId: string;
  message: string;
  userId: string;
}

export interface SessionStatusResponse {
  session: ProcessingSession;
  agentContext: any;
  agentStatuses: Record<string, AgentStatus>;
}

@Injectable({
  providedIn: 'root'
})
export class AgentService {
  private auth = inject(Auth);
  private functions = inject(Functions);
  
  // Observables for real-time updates
  private agentStatusSubject = new BehaviorSubject<Record<string, AgentStatus>>({});
  public agentStatus$ = this.agentStatusSubject.asObservable();

  private processingStatusSubject = new BehaviorSubject<string>('idle');
  public processingStatus$ = this.processingStatusSubject.asObservable();

  constructor() {}

  /**
   * Get authorization headers for API calls
   */
  private async getAuthHeaders(): Promise<Record<string, string>> {
    const user = this.auth.currentUser;
    if (!user) {
      throw new Error('User not authenticated');
    }

    const token = await user.getIdToken();
    return {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    };
  }

  /**
   * Call Firebase Functions
   */
  private callFunction(functionName: string, data: any): Observable<any> {
    const callable = httpsCallable(this.functions, functionName);
    return from(callable(data)).pipe(
      map((result: any) => result.data),
      catchError((error) => {
        console.error(`Firebase Function ${functionName} error:`, error);
        throw error;
      })
    );
  }

  /**
   * Start processing an audio file using Firebase Functions
   */
  startProcessing(request: ProcessingRequest): Observable<any> {
    this.processingStatusSubject.next('processing');

    return this.callFunction('processAudio', request).pipe(
      map((result: any) => {
        if (result.success) {
          this.processingStatusSubject.next('processing');
          return {
            success: true,
            data: {
              sessionId: result.sessionId,
              message: result.message || 'Processing started',
              status: 'processing'
            }
          };
        } else {
          this.processingStatusSubject.next('error');
          throw new Error(result.message || 'Processing failed');
        }
      }),
      catchError((error) => {
        this.processingStatusSubject.next('error');
        console.error('Processing error:', error);
        throw error;
      })
    );
  }

  /**
   * Send a chat message to the AI agent using Firebase Functions
   */
  sendChatMessage(request: ChatRequest): Observable<any> {
    return this.callFunction('chatWithAgent', request).pipe(
      map((result: any) => {
        if (result.success) {
          return {
            success: true,
            data: {
              sessionId: request.sessionId,
              message: result.message,
              success: true,
              nextAgent: null,
              shouldAskUser: false,
              chatHistory: [],
              agentContext: {
                sessionId: request.sessionId,
                userId: request.userId,
                memoriesCount: 0,
                agentStatusesCount: 0
              },
              workflowStatus: 'ready',
              updatedDescription: result.updatedDescription
            }
          };
        } else {
          throw new Error(result.message || 'Chat processing failed');
        }
      }),
      catchError((error) => {
        console.error('Chat error:', error);
        throw error;
      })
    );
  }



  /**
   * Get current session status using Firebase Functions
   */
  getSessionStatus(sessionId: string): Observable<SessionStatusResponse> {
    return this.callFunction('getSessionStatus', { sessionId }).pipe(
      map((result: any) => {
        return {
          session: result.session,
          agentContext: result.agentContext,
          agentStatuses: result.agentStatuses || {}
        };
      }),
      catchError((error) => {
        console.error('Session status error:', error);
        throw error;
      })
    );
  }

  /**
   * Continue auto mode processing using Firebase Functions
   */
  continueAutoMode(sessionId: string): Observable<any> {
    return this.callFunction('continueAutoMode', { sessionId }).pipe(
      map((result: any) => {
        if (result.success) {
          return {
            sessionId,
            success: result.success,
            message: result.message,
            agentStatus: result.agentStatus
          };
        } else {
          throw new Error(result.message || 'Failed to continue auto mode');
        }
      })
    );
  }



  /**
   * Start polling for status updates
   */
  private startStatusPolling(sessionId: string): void {
    interval(3000) // Poll every 3 seconds
      .pipe(
        switchMap(() => this.getSessionStatus(sessionId)),
        takeWhile((status) => {
          // Continue polling while processing
          const isProcessing = Object.values(status.agentStatuses || {})
            .some(agent => agent.status === 'processing');
          
          if (!isProcessing) {
            this.processingStatusSubject.next('complete');
          }
          
          return isProcessing;
        })
      )
      .subscribe({
        next: (status) => {
          this.agentStatusSubject.next(status.agentStatuses || {});
        },
        error: (error) => {
          console.error('Status polling error:', error);
          this.processingStatusSubject.next('error');
        }
      });
  }

  /**
   * Get current agent statuses
   */
  getCurrentAgentStatuses(): Record<string, AgentStatus> {
    return this.agentStatusSubject.value;
  }

  /**
   * Get processing status
   */
  getCurrentProcessingStatus(): string {
    return this.processingStatusSubject.value;
  }

  /**
   * Reset service state
   */
  reset(): void {
    this.agentStatusSubject.next({});
    this.processingStatusSubject.next('idle');
  }

  /**
   * Get agent progress summary
   */
  getProgressSummary(): Observable<any> {
    return this.agentStatus$.pipe(
      map(statuses => {
        const agents = Object.values(statuses);
        const totalAgents = agents.length;
        const completedAgents = agents.filter(agent => agent.status === 'complete').length;
        const processingAgents = agents.filter(agent => agent.status === 'processing');
        const errorAgents = agents.filter(agent => agent.status === 'error');

        const overallProgress = totalAgents > 0 ? (completedAgents / totalAgents) * 100 : 0;

        return {
          overallProgress,
          totalAgents,
          completedAgents,
          processingAgents: processingAgents.map(agent => ({
            name: agent.name,
            progress: agent.progress,
            message: agent.message
          })),
          errorAgents: errorAgents.map(agent => ({
            name: agent.name,
            message: agent.message
          })),
          isComplete: completedAgents === totalAgents && totalAgents > 0,
          hasErrors: errorAgents.length > 0
        };
      })
    );
  }

  /**
   * Retry failed agent
   */
  retryAgent(sessionId: string, agentName: string): Observable<any> {
    // This would trigger a specific agent retry
    // For now, we'll restart the entire process
    return this.continueAutoMode(sessionId);
  }

  /**
   * Check if processing is complete
   */
  isProcessingComplete(): Observable<boolean> {
    return this.agentStatus$.pipe(
      map(statuses => {
        const agents = Object.values(statuses);
        if (agents.length === 0) return false;
        
        return agents.every(agent => 
          agent.status === 'complete' || agent.status === 'error'
        );
      })
    );
  }

  /**
   * Get estimated time remaining
   */
  getEstimatedTimeRemaining(): Observable<number> {
    return this.agentStatus$.pipe(
      map(statuses => {
        const processingAgents = Object.values(statuses)
          .filter(agent => agent.status === 'processing');
        
        if (processingAgents.length === 0) return 0;

        // Simple estimation based on current progress
        const avgProgress = processingAgents.reduce((sum, agent) => sum + agent.progress, 0) / processingAgents.length;
        const remainingProgress = 100 - avgProgress;
        
        // Estimate 30 seconds per 10% progress
        return Math.round((remainingProgress / 10) * 30);
      })
    );
  }
}
