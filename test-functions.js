// Simple test script to verify Firebase Functions are working
const admin = require('firebase-admin');

// Initialize Firebase Admin with service account
// For testing purposes, we'll use the emulator
process.env.FIRESTORE_EMULATOR_HOST = 'localhost:8080';
process.env.FIREBASE_AUTH_EMULATOR_HOST = 'localhost:9099';

admin.initializeApp({
  projectId: 'pedma-ai'
});

async function testFunctions() {
  try {
    console.log('Testing Firebase Functions...');
    
    // Test health check function
    const response = await fetch('http://127.0.0.1:5001/pedma-ai/us-central1/healthCheck');
    const healthData = await response.json();
    console.log('Health Check:', healthData);
    
    console.log('Firebase Functions are working!');
  } catch (error) {
    console.error('Error testing functions:', error);
  }
}

testFunctions();
